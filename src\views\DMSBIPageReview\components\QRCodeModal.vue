<!--
 * @Author: othniel <EMAIL>
 * @Date: 2025-01-27 10:00:00
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-01-27 10:00:00
 * @FilePath: \pangea-component\src\views\DMSBIPageReview\components\QRCodeModal.vue
 * @Description: 二维码模态框组件
-->
<template>
  <a-modal 
    :visible="visible" 
    width="500px" 
    :footer="null" 
    :class="themeClasses"
    @cancel="handleCancel"
  >
    <template slot="title">
      <div class="modal-title">
        <span class="title-icon"></span>
        <span class="title-text">会议签到二维码</span>
      </div>
    </template>
    <div class="qrcode-content">
      <div v-if="qrCodeLoading" class="qrcode-loading">
        <a-spin size="large" />
        <p>正在获取二维码...</p>
      </div>
      <div v-else-if="qrCodeData.errorMsg" class="qrcode-error">
        <a-icon type="exclamation-circle" style="color: #ff4d4f; font-size: 48px;" />
        <p>{{ qrCodeData.errorMsg }}</p>
        <a-button type="primary" @click="refreshQRCode">重新获取</a-button>
      </div>
      <div v-else-if="qrCodeData.qrCodeLink" class="qrcode-success">
        <div class="qrcode-image-container" @click="refreshQRCode">
          <img :src="qrCodeData.qrCodeLink" alt="会议签到二维码" class="qrcode-image" />
          <div class="qrcode-refresh-overlay">
            <a-icon type="reload" />
            <span>点击刷新</span>
          </div>
        </div>
        <div class="qrcode-info">
          <p><strong>线体：</strong>{{ searchForm.lineName || '未选择' }}</p>
          <p v-if="qrCodeData.qrCodeCreateTime"><strong>创建时间：</strong>{{ formatTime(qrCodeData.qrCodeCreateTime) }}</p>
          <p v-if="qrCodeData.qrCodeExpiredTime"><strong>过期时间：</strong>{{ formatTime(qrCodeData.qrCodeExpiredTime) }}</p>
        </div>
      </div>
      <div v-else class="qrcode-empty">
        <a-icon type="qrcode" style="color: #d9d9d9; font-size: 48px;" />
        <p>请先选择线体后获取二维码</p>
        <a-button type="primary" @click="refreshQRCode" :disabled="!searchForm.lineCode">获取二维码</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
import moment from "moment";
import { getQrCode } from "../Api";

export default {
  name: "QRCodeModal",
  props: {
    // 模态框是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 是否为暗色主题
    darkTheme: {
      type: Boolean,
      default: false
    },
    // 搜索表单数据，包含线体信息
    searchForm: {
      type: Object,
      default: () => ({
        lineName: '',
        lineCode: '',
        workshopCode: ''
      })
    }
  },
  data() {
    return {
      // 二维码相关数据
      qrCodeData: {
        qrCodeLink: '', // 二维码图片链接
        qrCodeCreateTime: null, // 二维码创建时间
        qrCodeExpiredTime: null, // 二维码过期时间
        errorMsg: '' // 错误信息
      },
      qrCodeLoading: false // 二维码加载状态
    };
  },
  computed: {
    themeClasses() {
      return {
        "dark-theme": this.darkTheme,
      };
    }
  },
  watch: {
    // 监听模态框显示状态
    visible(newVal) {
      if (newVal) {
        // 模态框打开时，如果还没有二维码数据，自动获取
        if (!this.qrCodeData.qrCodeLink && !this.qrCodeLoading) {
          this.fetchQRCode();
        }
      }
    }
  },
  methods: {
    /**
     * 处理模态框取消事件
     */
    handleCancel() {
      this.$emit('update:visible', false);
    },

    /**
     * 获取二维码
     */
    async fetchQRCode() {
      if (!this.searchForm.lineCode) {
        this.qrCodeData.errorMsg = '请先选择线体';
        return;
      }

      try {
        this.qrCodeLoading = true;
        this.qrCodeData.errorMsg = '';

        console.log('获取二维码，workshopCode:', this.searchForm.workshopCode);
        const result = await getQrCode(this.searchForm.workshopCode);

        if (result) {
          this.qrCodeData = {
            qrCodeLink: this.getQRCodeImg(result.qrCodeLink) || '',
            qrCodeCreateTime: result.qrCodeCreateTime || null,
            qrCodeExpiredTime: result.qrCodeExpiredTime || null,
            errorMsg: result.errorMsg || ''
          };

          if (!this.qrCodeData.qrCodeLink && !this.qrCodeData.errorMsg) {
            this.qrCodeData.errorMsg = '获取二维码失败，请重试';
          }
        } else {
          this.qrCodeData.errorMsg = '获取二维码失败，请重试';
        }
      } catch (error) {
        console.error('获取二维码失败:', error);
        this.qrCodeData.errorMsg = '网络错误，请重试';
      } finally {
        this.qrCodeLoading = false;
      }
    },

    /**
     * 处理二维码图片URL
     */
    getQRCodeImg(url) {
      return url;
    },

    /**
     * 刷新二维码
     */
    async refreshQRCode() {
      // 清空当前二维码数据
      this.qrCodeData = {
        qrCodeLink: '',
        qrCodeCreateTime: null,
        qrCodeExpiredTime: null,
        errorMsg: ''
      };

      // 重新获取二维码
      await this.fetchQRCode();
    },

    /**
     * 格式化时间戳
     */
    formatTime(timestamp) {
      if (!timestamp) return '';
      return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style scoped>
/* 二维码模态框样式 */
.qrcode-content {
  text-align: center;
  padding: 20px;
}

.qrcode-loading {
  padding: 40px 20px;
}

.qrcode-loading p {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.qrcode-error {
  padding: 40px 20px;
}

.qrcode-error p {
  margin: 16px 0;
  color: #ff4d4f;
  font-size: 14px;
}

.qrcode-success {
  padding: 20px;
}

.qrcode-image-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.qrcode-image-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.qrcode-image {
  width: 200px;
  height: 200px;
  object-fit: contain;
  display: block;
}

.qrcode-refresh-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 14px;
}

.qrcode-image-container:hover .qrcode-refresh-overlay {
  opacity: 1;
}

.qrcode-refresh-overlay .anticon {
  font-size: 24px;
  margin-bottom: 8px;
}

.qrcode-info {
  text-align: left;
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.qrcode-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #333;
}

.qrcode-empty {
  padding: 40px 20px;
}

.qrcode-empty p {
  margin: 16px 0;
  color: #999;
  font-size: 14px;
}

/* 暗色主题下的二维码样式 */
.dark-theme .qrcode-loading p,
.dark-theme .qrcode-empty p {
  color: #ccc;
}

.dark-theme .qrcode-info {
  background: #001d2c;
  border: 1px solid #00aaa6;
}

.dark-theme .qrcode-info p {
  color: #fff;
}
</style>
