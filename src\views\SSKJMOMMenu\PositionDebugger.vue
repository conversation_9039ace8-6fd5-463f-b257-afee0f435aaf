<template>
    <div class="position-debugger">
        <div class="toolbar">
            <h3>节点位置调试工具</h3>
            <div class="controls">
                <a-button type="primary" @click="savePositions">保存位置到控制台</a-button>
                <a-button @click="resetPositions">重置位置</a-button>
                <a-button @click="toggleGrid">{{ showGrid ? '隐藏' : '显示' }}网格</a-button>
            </div>
        </div>

        <div class="canvas-container" ref="canvasContainer">
            <canvas
                ref="canvas"
                @mousedown="handleMouseDown"
                @mousemove="handleMouseMove"
                @mouseup="handleMouseUp"
                @mouseleave="handleMouseUp"
            ></canvas>

            <!-- 显示当前拖拽的节点信息 -->
            <div v-if="draggedNode" class="drag-info">
                拖拽中: {{ draggedNode.name }} ({{ Math.round(draggedNode.x) }}, {{ Math.round(draggedNode.y) }})
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PositionDebugger',
    data() {
        return {
            // Canvas相关
            canvas: null,
            ctx: null,
            canvasWidth: 0,
            canvasHeight: 0,
            scale: 1,

            // 显示控制
            showGrid: true,

            // 拖拽相关
            isDragging: false,
            draggedNode: null,
            dragOffset: { x: 0, y: 0 },

            // 图片资源
            images: {
                nodes: {},
                pipes: {}
            },
            isLoading: true,

            // 节点数据 - 按原始图片尺寸显示
            nodes: [
                {
                    id: 'demand-forecast',
                    name: '需求预测',
                    x: 150,
                    y: 120,
                    scale: 1, // 缩放比例，1为原始大小
                    topImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-base.png')
                },
                {
                    id: 'recruitment',
                    name: '人员招聘',
                    x: 450,
                    y: 120,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/recruitment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/recruitment-base.png')
                },
                {
                    id: 'promotion-management',
                    name: '晋升管理',
                    x: 750,
                    y: 120,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-base.png')
                },
                {
                    id: 'entry-training',
                    name: '入职培训',
                    x: 100,
                    y: 350,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/entry-training-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/entry-training-base.png')
                },
                {
                    id: 'job-deployment',
                    name: '在职调配',
                    x: 300,
                    y: 350,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-base.png')
                },
                {
                    id: 'performance-management',
                    name: '绩效管理',
                    x: 500,
                    y: 350,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/performance-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/performance-management-base.png')
                },
                {
                    id: 'resignation-management',
                    name: '离职管理',
                    x: 700,
                    y: 350,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-base.png')
                }
            ],

            // 管道图片配置 - 可拖拽的管道图片（按原始尺寸渲染）
            pipes: [
                {
                    id: 'pipe-1',
                    name: '管道1',
                    x: 290,
                    y: 170,
                    scale: 1, // 缩放比例，1为原始大小
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-1.png')
                },
                {
                    id: 'pipe-2',
                    name: '管道2',
                    x: 590,
                    y: 170,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-2.png')
                },
                {
                    id: 'pipe-3',
                    name: '管道3',
                    x: 220,
                    y: 220,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-3.png')
                },
                {
                    id: 'pipe-4',
                    name: '管道4',
                    x: 520,
                    y: 220,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-4.png')
                },
                {
                    id: 'pipe-5',
                    name: '管道5',
                    x: 820,
                    y: 220,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-5.png')
                },
                {
                    id: 'pipe-6',
                    name: '管道6',
                    x: 240,
                    y: 400,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-6.png')
                }
            ]
        }
    },

    mounted() {
        this.initCanvas()
        this.loadImages()
        this.bindEvents()
    },

    beforeDestroy() {
        this.unbindEvents()
    },

    methods: {
        // 初始化Canvas
        initCanvas() {
            this.canvas = this.$refs.canvas
            this.ctx = this.canvas.getContext('2d')
            this.resizeCanvas()
        },

        // 调整Canvas大小
        resizeCanvas() {
            const container = this.$refs.canvasContainer
            const containerRect = container.getBoundingClientRect()

            this.canvasWidth = containerRect.width
            this.canvasHeight = containerRect.height

            this.canvas.width = this.canvasWidth * window.devicePixelRatio
            this.canvas.height = this.canvasHeight * window.devicePixelRatio

            this.canvas.style.width = this.canvasWidth + 'px'
            this.canvas.style.height = this.canvasHeight + 'px'

            this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

            // 使用固定缩放比例便于调试
            this.scale = 1

            this.redraw()
        },

        // 加载图片资源
        async loadImages() {
            try {
                // 加载节点图片
                for (const node of this.nodes) {
                    const topImage = await this.loadImage(node.topImagePath)
                    const baseImage = await this.loadImage(node.baseImagePath)

                    this.images.nodes[node.id] = {
                        top: topImage,
                        base: baseImage
                    }

                    // 计算并保存节点的实际尺寸（只在加载时计算一次）
                    if (baseImage) {
                        node.actualWidth = (baseImage.naturalWidth || baseImage.width) * node.scale
                        node.actualHeight = (baseImage.naturalHeight || baseImage.height) * node.scale
                    } else {
                        node.actualWidth = 140 * node.scale
                        node.actualHeight = 100 * node.scale
                    }
                }

                // 加载管道图片
                for (const pipe of this.pipes) {
                    try {
                        const pipeImage = await this.loadImage(pipe.imagePath)
                        this.images.pipes[pipe.id] = pipeImage

                        // 计算并保存管道的实际尺寸（只在加载时计算一次）
                        if (pipeImage) {
                            pipe.actualWidth = (pipeImage.naturalWidth || pipeImage.width) * pipe.scale
                            pipe.actualHeight = (pipeImage.naturalHeight || pipeImage.height) * pipe.scale
                        } else {
                            pipe.actualWidth = 100 * pipe.scale
                            pipe.actualHeight = 20 * pipe.scale
                        }
                    } catch (error) {
                        console.warn(`管道 ${pipe.id} 图片加载失败`)
                        this.images.pipes[pipe.id] = null
                        pipe.actualWidth = 100 * pipe.scale
                        pipe.actualHeight = 20 * pipe.scale
                    }
                }

                this.isLoading = false
                this.redraw()
            } catch (error) {
                console.error('图片加载失败:', error)
                this.isLoading = false
                this.redraw()
            }
        },

        // 加载单个图片
        loadImage(src) {
            return new Promise((resolve) => {
                const img = new Image()
                img.onload = () => resolve(img)
                img.onerror = () => resolve(null)
                img.src = src
            })
        },

        // 重绘Canvas
        redraw() {
            if (!this.ctx) return

            this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

            // 绘制背景
            this.ctx.fillStyle = '#001529'
            this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

            // 绘制网格
            if (this.showGrid) {
                this.drawGrid()
            }

            if (this.isLoading) {
                this.drawLoading()
                return
            }

            // 绘制连接管道
            this.drawPipes()

            // 绘制节点
            this.drawNodes()
        },

        // 绘制网格
        drawGrid() {
            const gridSize = 50
            this.ctx.strokeStyle = '#333'
            this.ctx.lineWidth = 1
            this.ctx.setLineDash([2, 2])

            // 垂直线
            for (let x = 0; x <= this.canvasWidth; x += gridSize) {
                this.ctx.beginPath()
                this.ctx.moveTo(x, 0)
                this.ctx.lineTo(x, this.canvasHeight)
                this.ctx.stroke()
            }

            // 水平线
            for (let y = 0; y <= this.canvasHeight; y += gridSize) {
                this.ctx.beginPath()
                this.ctx.moveTo(0, y)
                this.ctx.lineTo(this.canvasWidth, y)
                this.ctx.stroke()
            }

            this.ctx.setLineDash([])
        },

        // 绘制加载状态
        drawLoading() {
            this.ctx.fillStyle = '#666'
            this.ctx.font = '16px Arial'
            this.ctx.textAlign = 'center'
            this.ctx.fillText('加载中...', this.canvasWidth / 2, this.canvasHeight / 2)
        },

        // 绘制管道图片
        drawPipes() {
            this.pipes.forEach(pipe => {
                const x = pipe.x * this.scale
                const y = pipe.y * this.scale
                const pipeImage = this.images.pipes[pipe.id]

                if (pipeImage) {
                    // 使用预先计算好的尺寸绘制管道图片
                    const scaledWidth = pipe.actualWidth * this.scale
                    const scaledHeight = pipe.actualHeight * this.scale

                    this.ctx.drawImage(pipeImage, x, y, scaledWidth, scaledHeight)

                    // 绘制管道名称（调试用）
                    this.ctx.fillStyle = '#ffffff'
                    this.ctx.font = `${Math.max(10, 12 * this.scale)}px Arial`
                    this.ctx.textAlign = 'center'
                    this.ctx.textBaseline = 'middle'
                    this.ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
                    this.ctx.shadowBlur = 4
                    this.ctx.fillText(pipe.name, x + scaledWidth / 2, y + scaledHeight / 2)
                    this.ctx.shadowColor = 'transparent'
                    this.ctx.shadowBlur = 0

                    // 高亮被拖拽的管道
                    if (this.draggedNode === pipe) {
                        this.ctx.strokeStyle = '#52c41a'
                        this.ctx.lineWidth = 3
                        this.ctx.strokeRect(x - 2, y - 2, scaledWidth + 4, scaledHeight + 4)
                    }
                } else {
                    // 默认绘制矩形占位（使用预先计算的尺寸）
                    const scaledWidth = pipe.actualWidth * this.scale
                    const scaledHeight = pipe.actualHeight * this.scale

                    this.ctx.fillStyle = 'rgba(0, 212, 255, 0.3)'
                    this.ctx.fillRect(x, y, scaledWidth, scaledHeight)
                    this.ctx.strokeStyle = '#00d4ff'
                    this.ctx.lineWidth = 2
                    this.ctx.strokeRect(x, y, scaledWidth, scaledHeight)

                    // 绘制管道名称
                    this.ctx.fillStyle = '#ffffff'
                    this.ctx.font = `${Math.max(10, 12 * this.scale)}px Arial`
                    this.ctx.textAlign = 'center'
                    this.ctx.textBaseline = 'middle'
                    this.ctx.fillText(pipe.name, x + scaledWidth / 2, y + scaledHeight / 2)

                    // 高亮被拖拽的管道
                    if (this.draggedNode === pipe) {
                        this.ctx.strokeStyle = '#52c41a'
                        this.ctx.lineWidth = 3
                        this.ctx.strokeRect(x - 2, y - 2, scaledWidth + 4, scaledHeight + 4)
                    }
                }
            })
        },

        // 绘制节点
        drawNodes() {
            this.nodes.forEach(node => {
                const x = node.x * this.scale
                const y = node.y * this.scale
                const nodeImages = this.images.nodes[node.id]

                if (nodeImages && nodeImages.base && nodeImages.top) {
                    // 绘制底座图片（使用预先计算的尺寸）
                    if (nodeImages.base) {
                        const baseWidth = node.actualWidth * this.scale
                        const baseHeight = node.actualHeight * this.scale

                        this.ctx.drawImage(nodeImages.base, x, y, baseWidth, baseHeight)
                    }

                    // 绘制顶部图片（按原始尺寸，稍微偏移营造3D效果）
                    if (nodeImages.top) {
                        const topWidth = (nodeImages.top.naturalWidth || nodeImages.top.width) * node.scale * this.scale
                        const topHeight = (nodeImages.top.naturalHeight || nodeImages.top.height) * node.scale * this.scale

                        // 顶部图片稍微向上偏移，营造3D效果
                        const offsetX = (node.actualWidth * this.scale - topWidth) / 2
                        const offsetY = (node.actualHeight * this.scale - topHeight) / 2 - 5 * this.scale

                        this.ctx.drawImage(nodeImages.top, x + offsetX, y + offsetY, topWidth, topHeight)
                    }
                } else {
                    // 默认绘制（使用预先计算的尺寸）
                    const scaledWidth = node.actualWidth * this.scale
                    const scaledHeight = node.actualHeight * this.scale

                    this.ctx.fillStyle = '#1890ff'
                    this.ctx.fillRect(x, y, scaledWidth, scaledHeight)
                    this.ctx.strokeStyle = '#00fbfb'
                    this.ctx.lineWidth = 2
                    this.ctx.strokeRect(x, y, scaledWidth, scaledHeight)
                }

                // 绘制节点名称
                this.ctx.fillStyle = '#ffffff'
                this.ctx.font = `bold ${Math.max(12, 14 * this.scale)}px Arial`
                this.ctx.textAlign = 'center'
                this.ctx.textBaseline = 'middle'

                // 添加文字阴影效果
                this.ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
                this.ctx.shadowBlur = 4
                this.ctx.shadowOffsetX = 1
                this.ctx.shadowOffsetY = 1

                const textY = y + node.actualHeight * this.scale + 20 * this.scale
                this.ctx.fillText(node.name, x + node.actualWidth * this.scale / 2, textY)

                // 重置阴影
                this.ctx.shadowColor = 'transparent'
                this.ctx.shadowBlur = 0
                this.ctx.shadowOffsetX = 0
                this.ctx.shadowOffsetY = 0

                // 高亮被拖拽的节点
                if (this.draggedNode === node) {
                    this.ctx.strokeStyle = '#52c41a'
                    this.ctx.lineWidth = 3
                    const nodeWidth = node.actualWidth * this.scale
                    const nodeHeight = node.actualHeight * this.scale
                    this.ctx.strokeRect(x - 2, y - 2, nodeWidth + 4, nodeHeight + 4)
                }
            })
        },

        // 鼠标按下事件
        handleMouseDown(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            // 检查是否点击了节点
            const clickedNode = this.getNodeAtPosition(x, y)

            if (clickedNode) {
                this.isDragging = true
                this.draggedNode = clickedNode
                this.dragOffset = {
                    x: x - clickedNode.x * this.scale,
                    y: y - clickedNode.y * this.scale
                }
                this.canvas.style.cursor = 'grabbing'
                this.canvas.classList.add('dragging')
            }
        },

        // 鼠标移动事件
        handleMouseMove(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            if (this.isDragging && this.draggedNode) {
                // 更新节点位置
                this.draggedNode.x = (x - this.dragOffset.x) / this.scale
                this.draggedNode.y = (y - this.dragOffset.y) / this.scale

                // 限制在画布范围内
                const elementWidth = this.draggedNode.actualWidth || 140
                const elementHeight = this.draggedNode.actualHeight || 100
                this.draggedNode.x = Math.max(0, Math.min(this.draggedNode.x, this.canvasWidth / this.scale - elementWidth))
                this.draggedNode.y = Math.max(0, Math.min(this.draggedNode.y, this.canvasHeight / this.scale - elementHeight))

                this.redraw()
            } else {
                // 检查鼠标悬停
                const hoveredNode = this.getNodeAtPosition(x, y)
                this.canvas.style.cursor = hoveredNode ? 'grab' : 'default'
            }
        },

        // 鼠标抬起事件
        handleMouseUp() {
            if (this.isDragging) {
                this.isDragging = false
                this.draggedNode = null
                this.canvas.style.cursor = 'default'
                this.canvas.classList.remove('dragging')
                this.redraw()
            }
        },

        // 获取指定位置的节点或管道
        getNodeAtPosition(x, y) {
            // 先检查节点（节点优先级更高）
            for (const node of this.nodes) {
                const nodeX = node.x * this.scale
                const nodeY = node.y * this.scale
                const nodeWidth = (node.actualWidth || 140) * this.scale
                const nodeHeight = (node.actualHeight || 100) * this.scale

                // 包含文字区域
                const textAreaHeight = 25 * this.scale
                const totalHeight = nodeHeight + textAreaHeight

                if (x >= nodeX && x <= nodeX + nodeWidth &&
                    y >= nodeY && y <= nodeY + totalHeight) {
                    return node
                }
            }

            // 再检查管道
            for (const pipe of this.pipes) {
                const pipeX = pipe.x * this.scale
                const pipeY = pipe.y * this.scale
                const pipeWidth = (pipe.actualWidth || 100) * this.scale
                const pipeHeight = (pipe.actualHeight || 20) * this.scale

                if (x >= pipeX && x <= pipeX + pipeWidth &&
                    y >= pipeY && y <= pipeY + pipeHeight) {
                    return pipe
                }
            }

            return null
        },

        // 保存位置到控制台
        savePositions() {
            const nodePositions = this.nodes.map(node => ({
                id: node.id,
                name: node.name,
                x: Math.round(node.x),
                y: Math.round(node.y),
                scale: node.scale,
                actualWidth: Math.round(node.actualWidth || 140),
                actualHeight: Math.round(node.actualHeight || 100)
            }))

            const pipePositions = this.pipes.map(pipe => ({
                id: pipe.id,
                name: pipe.name,
                x: Math.round(pipe.x),
                y: Math.round(pipe.y),
                scale: pipe.scale,
                actualWidth: Math.round(pipe.actualWidth || 100),
                actualHeight: Math.round(pipe.actualHeight || 20)
            }))

            console.log('=== 节点位置配置 ===')
            console.log('复制以下代码到原始组件的nodes数组中：')
            console.log('')

            nodePositions.forEach(pos => {
                console.log(`{`)
                console.log(`    id: '${pos.id}',`)
                console.log(`    name: '${pos.name}',`)
                console.log(`    x: ${pos.x},`)
                console.log(`    y: ${pos.y},`)
                console.log(`    scale: ${pos.scale}, // 缩放比例，1为原始大小`)
                console.log(`    // 实际渲染尺寸: ${pos.actualWidth}x${pos.actualHeight}`)
                console.log(`    topImagePath: require('@/assets/images/SSKJMomMenu/${pos.id}-top.png'),`)
                console.log(`    baseImagePath: require('@/assets/images/SSKJMomMenu/${pos.id}-base.png'),`)
                console.log(`    menuItems: [`)
                console.log(`        // 在这里添加菜单项`)
                console.log(`    ]`)
                console.log(`},`)
                console.log('')
            })

            console.log('=== 管道位置配置 ===')
            console.log('复制以下代码到原始组件的pipes数组中：')
            console.log('')

            pipePositions.forEach(pos => {
                console.log(`{`)
                console.log(`    id: '${pos.id}',`)
                console.log(`    name: '${pos.name}',`)
                console.log(`    x: ${pos.x},`)
                console.log(`    y: ${pos.y},`)
                console.log(`    scale: ${pos.scale}, // 缩放比例，1为原始大小`)
                console.log(`    // 实际渲染尺寸: ${pos.actualWidth}x${pos.actualHeight}`)
                console.log(`    imagePath: require('@/assets/images/SSKJMomMenu/${pos.id}.png')`)
                console.log(`},`)
                console.log('')
            })

            this.$message.success('位置信息已输出到控制台，请按F12查看')
        },

        // 重置位置
        resetPositions() {
            // 恢复节点初始位置
            const initialNodePositions = [
                { id: 'demand-forecast', x: 150, y: 120, scale: 1 },
                { id: 'recruitment', x: 450, y: 120, scale: 1 },
                { id: 'promotion-management', x: 750, y: 120, scale: 1 },
                { id: 'entry-training', x: 100, y: 350, scale: 1 },
                { id: 'job-deployment', x: 300, y: 350, scale: 1 },
                { id: 'performance-management', x: 500, y: 350, scale: 1 },
                { id: 'resignation-management', x: 700, y: 350, scale: 1 }
            ]

            // 恢复管道初始位置
            const initialPipePositions = [
                { id: 'pipe-1', x: 290, y: 170, scale: 1 },
                { id: 'pipe-2', x: 590, y: 170, scale: 1 },
                { id: 'pipe-3', x: 220, y: 220, scale: 1 },
                { id: 'pipe-4', x: 520, y: 220, scale: 1 },
                { id: 'pipe-5', x: 820, y: 220, scale: 1 },
                { id: 'pipe-6', x: 240, y: 400, scale: 1 }
            ]

            initialNodePositions.forEach(pos => {
                const node = this.nodes.find(n => n.id === pos.id)
                if (node) {
                    node.x = pos.x
                    node.y = pos.y
                    node.scale = pos.scale
                }
            })

            initialPipePositions.forEach(pos => {
                const pipe = this.pipes.find(p => p.id === pos.id)
                if (pipe) {
                    pipe.x = pos.x
                    pipe.y = pos.y
                    pipe.scale = pos.scale
                }
            })

            this.redraw()
            this.$message.info('节点和管道位置已重置')
        },

        // 切换网格显示
        toggleGrid() {
            this.showGrid = !this.showGrid
            this.redraw()
        },

        // 绑定事件
        bindEvents() {
            window.addEventListener('resize', this.handleResize)
        },

        // 解绑事件
        unbindEvents() {
            window.removeEventListener('resize', this.handleResize)
        },

        // 窗口大小改变事件
        handleResize() {
            this.resizeCanvas()
        }
    }
}
</script>

<style lang="less" scoped>
.position-debugger {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f0f2f5;

    .toolbar {
        padding: 16px;
        background: #fff;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            margin: 0;
            color: #262626;
        }

        .controls {
            display: flex;
            gap: 12px;
        }
    }

    .canvas-container {
        flex: 1;
        position: relative;
        overflow: hidden;

        canvas {
            display: block;
            width: 100%;
            height: 100%;
            cursor: grab;

            &.dragging {
                cursor: grabbing;
            }
        }

        .drag-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
    }
}
</style>
