<template>
    <div class="position-debugger">
        <div class="toolbar">
            <h3>节点位置调试工具</h3>
            <div class="controls">
                <a-button type="primary" @click="savePositions">保存位置到控制台</a-button>
                <a-button @click="resetPositions">重置位置</a-button>
                <a-button @click="toggleGrid">{{ showGrid ? '隐藏' : '显示' }}网格</a-button>
            </div>
        </div>

        <div class="canvas-container" ref="canvasContainer">
            <canvas
                ref="canvas"
                @mousedown="handleMouseDown"
                @mousemove="handleMouseMove"
                @mouseup="handleMouseUp"
                @mouseleave="handleMouseUp"
            ></canvas>

            <!-- 显示当前拖拽的节点信息 -->
            <div v-if="draggedNode" class="drag-info">
                拖拽中: {{ draggedNode.name }} ({{ Math.round(draggedNode.x) }}, {{ Math.round(draggedNode.y) }})
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'PositionDebugger',
    data() {
        return {
            // Canvas相关
            canvas: null,
            ctx: null,
            canvasWidth: 0,
            canvasHeight: 0,
            scale: 1,

            // 显示控制
            showGrid: true,

            // 拖拽相关
            isDragging: false,
            draggedNode: null,
            dragOffset: { x: 0, y: 0 },

            // 图片资源
            images: {
                nodes: {},
                pipes: {}
            },
            isLoading: true,

            // 节点数据 - 复制自原始组件
            nodes: [
                {
                    id: 'demand-forecast',
                    name: '需求预测',
                    x: 150,
                    y: 120,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-base.png')
                },
                {
                    id: 'recruitment',
                    name: '人员招聘',
                    x: 450,
                    y: 120,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/recruitment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/recruitment-base.png')
                },
                {
                    id: 'promotion-management',
                    name: '晋升管理',
                    x: 750,
                    y: 120,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-base.png')
                },
                {
                    id: 'entry-training',
                    name: '入职培训',
                    x: 100,
                    y: 350,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/entry-training-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/entry-training-base.png')
                },
                {
                    id: 'job-deployment',
                    name: '在职调配',
                    x: 300,
                    y: 350,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-base.png')
                },
                {
                    id: 'performance-management',
                    name: '绩效管理',
                    x: 500,
                    y: 350,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/performance-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/performance-management-base.png')
                },
                {
                    id: 'resignation-management',
                    name: '离职管理',
                    x: 700,
                    y: 350,
                    width: 140,
                    height: 100,
                    topImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-base.png')
                }
            ],

            // 连接管道配置 - 复制自原始组件
            pipes: [
                // 上层横向连接
                { from: 'demand-forecast', to: 'recruitment', imagePath: require('@/assets/images/SSKJMomMenu/pipe-1.png') },
                { from: 'recruitment', to: 'promotion-management', imagePath: require('@/assets/images/SSKJMomMenu/pipe-2.png') },

                // 纵向连接
                { from: 'demand-forecast', to: 'entry-training', imagePath: require('@/assets/images/SSKJMomMenu/pipe-3.png') },
                { from: 'recruitment', to: 'job-deployment', imagePath: require('@/assets/images/SSKJMomMenu/pipe-4.png') },
                { from: 'promotion-management', to: 'resignation-management', imagePath: require('@/assets/images/SSKJMomMenu/pipe-5.png') },

                // 下层横向连接
                { from: 'entry-training', to: 'job-deployment', imagePath: require('@/assets/images/SSKJMomMenu/pipe-6.png') },
                { from: 'job-deployment', to: 'performance-management', imagePath: require('@/assets/images/SSKJMomMenu/pipe-1.png') },
                { from: 'performance-management', to: 'resignation-management', imagePath: require('@/assets/images/SSKJMomMenu/pipe-2.png') }
            ]
        }
    },

    mounted() {
        this.initCanvas()
        this.loadImages()
        this.bindEvents()
    },

    beforeDestroy() {
        this.unbindEvents()
    },

    methods: {
        // 初始化Canvas
        initCanvas() {
            this.canvas = this.$refs.canvas
            this.ctx = this.canvas.getContext('2d')
            this.resizeCanvas()
        },

        // 调整Canvas大小
        resizeCanvas() {
            const container = this.$refs.canvasContainer
            const containerRect = container.getBoundingClientRect()

            this.canvasWidth = containerRect.width
            this.canvasHeight = containerRect.height

            this.canvas.width = this.canvasWidth * window.devicePixelRatio
            this.canvas.height = this.canvasHeight * window.devicePixelRatio

            this.canvas.style.width = this.canvasWidth + 'px'
            this.canvas.style.height = this.canvasHeight + 'px'

            this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

            // 使用固定缩放比例便于调试
            this.scale = 1

            this.redraw()
        },

        // 加载图片资源
        async loadImages() {
            try {
                // 加载节点图片
                for (const node of this.nodes) {
                    this.images.nodes[node.id] = {
                        top: await this.loadImage(node.topImagePath),
                        base: await this.loadImage(node.baseImagePath)
                    }
                }

                // 加载管道图片
                for (const pipe of this.pipes) {
                    try {
                        this.images.pipes[`${pipe.from}-${pipe.to}`] = await this.loadImage(pipe.imagePath)
                    } catch (error) {
                        console.warn(`管道 ${pipe.from}-${pipe.to} 图片加载失败，将使用默认连接线`)
                        this.images.pipes[`${pipe.from}-${pipe.to}`] = null
                    }
                }

                this.isLoading = false
                this.redraw()
            } catch (error) {
                console.error('图片加载失败:', error)
                this.isLoading = false
                this.redraw()
            }
        },

        // 加载单个图片
        loadImage(src) {
            return new Promise((resolve) => {
                const img = new Image()
                img.onload = () => resolve(img)
                img.onerror = () => resolve(null)
                img.src = src
            })
        },

        // 重绘Canvas
        redraw() {
            if (!this.ctx) return

            this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

            // 绘制背景
            this.ctx.fillStyle = '#001529'
            this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

            // 绘制网格
            if (this.showGrid) {
                this.drawGrid()
            }

            if (this.isLoading) {
                this.drawLoading()
                return
            }

            // 绘制连接管道
            this.drawPipes()

            // 绘制节点
            this.drawNodes()
        },

        // 绘制网格
        drawGrid() {
            const gridSize = 50
            this.ctx.strokeStyle = '#333'
            this.ctx.lineWidth = 1
            this.ctx.setLineDash([2, 2])

            // 垂直线
            for (let x = 0; x <= this.canvasWidth; x += gridSize) {
                this.ctx.beginPath()
                this.ctx.moveTo(x, 0)
                this.ctx.lineTo(x, this.canvasHeight)
                this.ctx.stroke()
            }

            // 水平线
            for (let y = 0; y <= this.canvasHeight; y += gridSize) {
                this.ctx.beginPath()
                this.ctx.moveTo(0, y)
                this.ctx.lineTo(this.canvasWidth, y)
                this.ctx.stroke()
            }

            this.ctx.setLineDash([])
        },

        // 绘制加载状态
        drawLoading() {
            this.ctx.fillStyle = '#666'
            this.ctx.font = '16px Arial'
            this.ctx.textAlign = 'center'
            this.ctx.fillText('加载中...', this.canvasWidth / 2, this.canvasHeight / 2)
        },

        // 绘制连接管道
        drawPipes() {
            this.pipes.forEach(pipe => {
                const fromNode = this.nodes.find(n => n.id === pipe.from)
                const toNode = this.nodes.find(n => n.id === pipe.to)

                if (!fromNode || !toNode) return

                const pipeImage = this.images.pipes[`${pipe.from}-${pipe.to}`]

                if (pipeImage) {
                    // 计算连接点位置
                    const fromCenterX = (fromNode.x + fromNode.width / 2) * this.scale
                    const fromCenterY = (fromNode.y + fromNode.height / 2) * this.scale
                    const toCenterX = (toNode.x + toNode.width / 2) * this.scale
                    const toCenterY = (toNode.y + toNode.height / 2) * this.scale

                    // 根据连接方向调整管道绘制
                    if (Math.abs(fromCenterY - toCenterY) < 50) {
                        // 水平连接
                        const startX = Math.min(fromCenterX, toCenterX)
                        const endX = Math.max(fromCenterX, toCenterX)
                        const y = (fromCenterY + toCenterY) / 2

                        this.ctx.drawImage(
                            pipeImage,
                            startX,
                            y - 10,
                            endX - startX,
                            20
                        )
                    } else {
                        // 垂直连接
                        const startY = Math.min(fromCenterY, toCenterY)
                        const endY = Math.max(fromCenterY, toCenterY)
                        const x = (fromCenterX + toCenterX) / 2

                        this.ctx.drawImage(
                            pipeImage,
                            x - 10,
                            startY,
                            20,
                            endY - startY
                        )
                    }
                } else {
                    // 默认绘制连接线
                    this.drawDefaultPipe(fromNode, toNode)
                }
            })
        },

        // 绘制默认连接线
        drawDefaultPipe(fromNode, toNode) {
            const fromCenterX = (fromNode.x + fromNode.width / 2) * this.scale
            const fromCenterY = (fromNode.y + fromNode.height / 2) * this.scale
            const toCenterX = (toNode.x + toNode.width / 2) * this.scale
            const toCenterY = (toNode.y + toNode.height / 2) * this.scale

            // 绘制科技感连接线
            this.ctx.strokeStyle = '#00d4ff'
            this.ctx.lineWidth = 3
            this.ctx.shadowColor = '#00d4ff'
            this.ctx.shadowBlur = 10
            this.ctx.setLineDash([])

            this.ctx.beginPath()
            this.ctx.moveTo(fromCenterX, fromCenterY)
            this.ctx.lineTo(toCenterX, toCenterY)
            this.ctx.stroke()

            // 重置阴影
            this.ctx.shadowColor = 'transparent'
            this.ctx.shadowBlur = 0

            // 在连接点绘制小圆点
            this.drawConnectionPoint(fromCenterX, fromCenterY)
            this.drawConnectionPoint(toCenterX, toCenterY)
        },

        // 绘制连接点
        drawConnectionPoint(x, y) {
            this.ctx.fillStyle = '#00d4ff'
            this.ctx.beginPath()
            this.ctx.arc(x, y, 4, 0, 2 * Math.PI)
            this.ctx.fill()

            // 添加发光效果
            this.ctx.shadowColor = '#00d4ff'
            this.ctx.shadowBlur = 8
            this.ctx.beginPath()
            this.ctx.arc(x, y, 2, 0, 2 * Math.PI)
            this.ctx.fill()

            // 重置阴影
            this.ctx.shadowColor = 'transparent'
            this.ctx.shadowBlur = 0
        },

        // 绘制节点
        drawNodes() {
            this.nodes.forEach(node => {
                const x = node.x * this.scale
                const y = node.y * this.scale
                const width = node.width * this.scale
                const height = node.height * this.scale

                const nodeImages = this.images.nodes[node.id]

                if (nodeImages && nodeImages.base && nodeImages.top) {
                    // 绘制底座
                    if (nodeImages.base) {
                        this.ctx.drawImage(nodeImages.base, x, y, width, height)
                    }

                    // 绘制顶部图片
                    if (nodeImages.top) {
                        const topHeight = height * 0.8
                        const topWidth = width * 0.9
                        const topX = x + (width - topWidth) / 2
                        const topY = y + (height - topHeight) / 2 - height * 0.05

                        this.ctx.drawImage(nodeImages.top, topX, topY, topWidth, topHeight)
                    }
                } else {
                    // 默认绘制
                    this.ctx.fillStyle = '#1890ff'
                    this.ctx.fillRect(x, y, width, height)
                    this.ctx.strokeStyle = '#00fbfb'
                    this.ctx.lineWidth = 2
                    this.ctx.strokeRect(x, y, width, height)
                }

                // 绘制节点名称
                this.ctx.fillStyle = '#ffffff'
                this.ctx.font = 'bold 14px Arial'
                this.ctx.textAlign = 'center'
                this.ctx.textBaseline = 'middle'
                this.ctx.fillText(node.name, x + width / 2, y + height + 20)

                // 高亮被拖拽的节点
                if (this.draggedNode === node) {
                    this.ctx.strokeStyle = '#52c41a'
                    this.ctx.lineWidth = 3
                    this.ctx.strokeRect(x - 2, y - 2, width + 4, height + 4)
                }
            })
        },

        // 鼠标按下事件
        handleMouseDown(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            // 检查是否点击了节点
            const clickedNode = this.getNodeAtPosition(x, y)

            if (clickedNode) {
                this.isDragging = true
                this.draggedNode = clickedNode
                this.dragOffset = {
                    x: x - clickedNode.x * this.scale,
                    y: y - clickedNode.y * this.scale
                }
                this.canvas.style.cursor = 'grabbing'
                this.canvas.classList.add('dragging')
            }
        },

        // 鼠标移动事件
        handleMouseMove(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            if (this.isDragging && this.draggedNode) {
                // 更新节点位置
                this.draggedNode.x = (x - this.dragOffset.x) / this.scale
                this.draggedNode.y = (y - this.dragOffset.y) / this.scale

                // 限制在画布范围内
                this.draggedNode.x = Math.max(0, Math.min(this.draggedNode.x, this.canvasWidth / this.scale - this.draggedNode.width))
                this.draggedNode.y = Math.max(0, Math.min(this.draggedNode.y, this.canvasHeight / this.scale - this.draggedNode.height))

                this.redraw()
            } else {
                // 检查鼠标悬停
                const hoveredNode = this.getNodeAtPosition(x, y)
                this.canvas.style.cursor = hoveredNode ? 'grab' : 'default'
            }
        },

        // 鼠标抬起事件
        handleMouseUp() {
            if (this.isDragging) {
                this.isDragging = false
                this.draggedNode = null
                this.canvas.style.cursor = 'default'
                this.canvas.classList.remove('dragging')
                this.redraw()
            }
        },

        // 获取指定位置的节点
        getNodeAtPosition(x, y) {
            for (const node of this.nodes) {
                const nodeX = node.x * this.scale
                const nodeY = node.y * this.scale
                const nodeWidth = node.width * this.scale
                const nodeHeight = node.height * this.scale

                // 包含文字区域
                const textAreaHeight = 25
                const totalHeight = nodeHeight + textAreaHeight

                if (x >= nodeX && x <= nodeX + nodeWidth &&
                    y >= nodeY && y <= nodeY + totalHeight) {
                    return node
                }
            }
            return null
        },

        // 保存位置到控制台
        savePositions() {
            const positions = this.nodes.map(node => ({
                id: node.id,
                name: node.name,
                x: Math.round(node.x),
                y: Math.round(node.y),
                width: node.width,
                height: node.height
            }))

            console.log('=== 节点位置配置 ===')
            console.log('复制以下代码到原始组件的nodes数组中：')
            console.log('')

            positions.forEach(pos => {
                console.log(`{`)
                console.log(`    id: '${pos.id}',`)
                console.log(`    name: '${pos.name}',`)
                console.log(`    x: ${pos.x},`)
                console.log(`    y: ${pos.y},`)
                console.log(`    width: ${pos.width},`)
                console.log(`    height: ${pos.height},`)
                console.log(`    topImagePath: require('@/assets/images/SSKJMomMenu/${pos.id}-top.png'),`)
                console.log(`    baseImagePath: require('@/assets/images/SSKJMomMenu/${pos.id}-base.png'),`)
                console.log(`    menuItems: [`)
                console.log(`        // 在这里添加菜单项`)
                console.log(`    ]`)
                console.log(`},`)
                console.log('')
            })

            this.$message.success('位置信息已输出到控制台，请按F12查看')
        },

        // 重置位置
        resetPositions() {
            // 恢复初始位置
            const initialPositions = [
                { id: 'demand-forecast', x: 150, y: 120 },
                { id: 'recruitment', x: 450, y: 120 },
                { id: 'promotion-management', x: 750, y: 120 },
                { id: 'entry-training', x: 100, y: 350 },
                { id: 'job-deployment', x: 300, y: 350 },
                { id: 'performance-management', x: 500, y: 350 },
                { id: 'resignation-management', x: 700, y: 350 }
            ]

            initialPositions.forEach(pos => {
                const node = this.nodes.find(n => n.id === pos.id)
                if (node) {
                    node.x = pos.x
                    node.y = pos.y
                }
            })

            this.redraw()
            this.$message.info('位置已重置')
        },

        // 切换网格显示
        toggleGrid() {
            this.showGrid = !this.showGrid
            this.redraw()
        },

        // 绑定事件
        bindEvents() {
            window.addEventListener('resize', this.handleResize)
        },

        // 解绑事件
        unbindEvents() {
            window.removeEventListener('resize', this.handleResize)
        },

        // 窗口大小改变事件
        handleResize() {
            this.resizeCanvas()
        }
    }
}
</script>

<style lang="less" scoped>
.position-debugger {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f0f2f5;

    .toolbar {
        padding: 16px;
        background: #fff;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
            margin: 0;
            color: #262626;
        }

        .controls {
            display: flex;
            gap: 12px;
        }
    }

    .canvas-container {
        flex: 1;
        position: relative;
        overflow: hidden;

        canvas {
            display: block;
            width: 100%;
            height: 100%;
            cursor: grab;

            &.dragging {
                cursor: grabbing;
            }
        }

        .drag-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: #fff;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-family: monospace;
        }
    }
}
</style>
