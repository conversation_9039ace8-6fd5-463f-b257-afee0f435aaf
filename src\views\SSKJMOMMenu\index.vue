<template>
    <div class="sskj-mom-menu-container">
        <!-- Canvas容器 -->
        <div class="canvas-container" ref="canvasContainer">
            <canvas ref="canvas" @click="handleCanvasClick" @mousemove="handleCanvasMouseMove"
                @mouseleave="handleCanvasMouseLeave"></canvas>
        </div>

        <!-- 节点菜单 -->
        <div v-if="showMenu" class="node-menu" :style="menuStyle" @click.stop>
            <div class="menu-header">
                <span class="menu-title">{{ currentNode.name }}</span>
                <a-icon type="close" class="close-btn" @click="closeMenu" />
            </div>
            <div class="menu-content">
                <div v-for="(item, index) in currentNode.menuItems" :key="index" class="menu-item"
                    @click="handleMenuItemClick(item)">
                    <a-icon :type="item.icon" v-if="item.icon" />
                    <span>{{ item.label }}</span>
                </div>
            </div>
        </div>

        <!-- 遮罩层 -->
        <div v-if="showMenu" class="menu-overlay" @click="closeMenu"></div>
    </div>
</template>

<script>
export default {
    name: 'SSKJMOMMenu',
    data() {
        return {
            // Canvas相关
            canvas: null,
            ctx: null,
            canvasWidth: 0,
            canvasHeight: 0,
            scale: 1, // 缩放比例

            // 图片资源
            images: {
                background: null,
                nodes: {},
                pipes: {}
            },

            // 节点数据 - 按原始图片尺寸显示
            nodes: [
                {
                    id: 'demand-forecast',
                    name: '需求预测',
                    x: 223,
                    y: 96,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-base.png'),
                    // 图片位置微调
                    baseOffset: { x: -20, y: 60 }, // 底座图片偏移
                    topOffset: { x: 0, y: -5 }, // 顶部图片偏移
                    menuItems: [
                        { label: '招聘渠道分析', icon: 'bar-chart', action: 'analyze' },
                        { label: 'AI面试', icon: 'robot', action: 'ai-interview' }
                    ]
                },
                {
                    id: 'recruitment',
                    name: '人员招聘',
                    x: 660,
                    y: 110,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/recruitment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/recruitment-base.png'),
                    baseOffset: { x: -28, y: 48 },
                    topOffset: { x: 0, y: -5 },
                    menuItems: [
                        { label: '招聘流程', icon: 'user-add', action: 'process' },
                        { label: '面试安排', icon: 'calendar', action: 'interview' },
                        { label: '人员统计', icon: 'team', action: 'statistics' }
                    ]
                },
                {
                    id: 'promotion-management',
                    name: '晋升管理',
                    x: 1249,
                    y: 141,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-base.png'),
                    baseOffset: { x: -30, y: 60 },
                    topOffset: { x: 0, y: -5 },
                    menuItems: [
                        { label: '培训课程', icon: 'read', action: 'courses' },
                        { label: '考试安排', icon: 'file-search', action: 'exam' },
                        { label: '培训进度', icon: 'progress', action: 'progress' }
                    ]
                },
                {
                    id: 'entry-training',
                    name: '入职培训',
                    x: 96,
                    y: 372,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/entry-training-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/entry-training-base.png'),
                    baseOffset: { x: -10, y: 50 },
                    topOffset: { x: 0, y: -5 },
                    menuItems: [
                        { label: 'AI配置', icon: 'robot', action: 'ai-config' },
                        { label: '面试记录', icon: 'file-text', action: 'records' },
                        { label: '评估报告', icon: 'file-done', action: 'report' }
                    ]
                },
                {
                    id: 'job-deployment',
                    name: '在职调配',
                    x: 606,
                    y: 481,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-base.png'),
                    baseOffset: { x: -45, y: 45 },
                    topOffset: { x: 0, y: -5 },
                    menuItems: [
                        { label: '培训计划', icon: 'book', action: 'training' },
                        { label: '技能评估', icon: 'trophy', action: 'assessment' },
                        { label: '证书管理', icon: 'safety-certificate', action: 'certificate' }
                    ]
                },
                {
                    id: 'performance-management',
                    name: '绩效管理',
                    x: 922,
                    y: 294,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/performance-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/performance-management-base.png'),
                    baseOffset: { x: -20, y: 70 },
                    topOffset: { x: 0, y: -5 },
                    menuItems: [
                        { label: '绩效考核', icon: 'audit', action: 'evaluation' },
                        { label: '目标设定', icon: 'aim', action: 'target' },
                        { label: '奖惩记录', icon: 'gift', action: 'reward' }
                    ]
                },
                {
                    id: 'resignation-management',
                    name: '离职管理',
                    x: 1352,
                    y: 477,
                    scale: 1,
                    topImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-base.png'),
                    baseOffset: { x: -30, y: 30 },
                    topOffset: { x: 0, y: -5 },
                    menuItems: [
                        { label: '离职流程', icon: 'logout', action: 'process' },
                        { label: '交接安排', icon: 'swap', action: 'handover' },
                        { label: '离职分析', icon: 'pie-chart', action: 'analysis' }
                    ]
                }
            ],

            // 管道图片配置 - 可拖拽的管道图片（按原始尺寸渲染）
            pipes: [
                {
                    id: 'pipe-1',
                    name: '管道1',
                    x: 492,
                    y: 133,
                    scale: 1, // 缩放比例，1为原始大小
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-1.png')
                },
                {
                    id: 'pipe-2',
                    name: '管道2',
                    x: 433,
                    y: 297,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-2.png')
                },
                {
                    id: 'pipe-3',
                    name: '管道3',
                    x: 405,
                    y: 588,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-3.png')
                },
                {
                    id: 'pipe-4',
                    name: '管道4',
                    x: 887,
                    y: 490,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-4.png')
                },
                {
                    id: 'pipe-5',
                    name: '管道5',
                    x: 1197,
                    y: 321,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-5.png')
                },
                {
                    id: 'pipe-6',
                    name: '管道6',
                    x: 1494,
                    y: 339,
                    scale: 1,
                    imagePath: require('@/assets/images/SSKJMomMenu/pipe-6.png')
                }
            ],

            // 菜单相关
            showMenu: false,
            currentNode: null,
            menuStyle: {},

            // 交互状态
            hoveredNode: null,
            isLoading: true
        }
    },

    mounted() {
        this.initCanvas()
        this.loadImages()
        this.bindEvents()
    },

    beforeDestroy() {
        this.unbindEvents()
    },

    methods: {
        // 初始化Canvas
        initCanvas() {
            this.canvas = this.$refs.canvas
            this.ctx = this.canvas.getContext('2d')
            this.resizeCanvas()
        },

        // 调整Canvas大小
        resizeCanvas() {
            const container = this.$refs.canvasContainer
            const containerRect = container.getBoundingClientRect()

            // 设置Canvas尺寸
            this.canvasWidth = containerRect.width
            this.canvasHeight = containerRect.height

            // 设置Canvas实际尺寸
            this.canvas.width = this.canvasWidth * window.devicePixelRatio
            this.canvas.height = this.canvasHeight * window.devicePixelRatio

            // 设置Canvas显示尺寸
            this.canvas.style.width = this.canvasWidth + 'px'
            this.canvas.style.height = this.canvasHeight + 'px'

            // 缩放上下文以适应高DPI
            this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

            // 使用固定缩放比例，与调试工具保持一致
            this.scale = 1

            this.redraw()
        },

        // 加载图片资源
        async loadImages() {
            try {

                // 加载节点图片（每个节点有两张图片：顶部图片和底座图片）
                for (const node of this.nodes) {
                    try {
                        const topImage = await this.loadImage(node.topImagePath)
                        const baseImage = await this.loadImage(node.baseImagePath)

                        this.images.nodes[node.id] = {
                            top: topImage,
                            base: baseImage
                        }

                        // 计算并保存节点的实际尺寸
                        if (baseImage) {
                            node.actualWidth = (baseImage.naturalWidth || baseImage.width) * node.scale
                            node.actualHeight = (baseImage.naturalHeight || baseImage.height) * node.scale
                        } else {
                            node.actualWidth = 140 * node.scale
                            node.actualHeight = 100 * node.scale
                        }
                    } catch (error) {
                        console.warn(`节点 ${node.id} 图片加载失败，将使用默认样式`)
                        this.images.nodes[node.id] = {
                            top: null,
                            base: null
                        }
                        node.actualWidth = 140 * node.scale
                        node.actualHeight = 100 * node.scale
                    }
                }

                // 加载管道图片
                for (const pipe of this.pipes) {
                    try {
                        const pipeImage = await this.loadImage(pipe.imagePath)
                        this.images.pipes[pipe.id] = pipeImage

                        // 计算并保存管道的实际尺寸
                        if (pipeImage) {
                            pipe.actualWidth = (pipeImage.naturalWidth || pipeImage.width) * pipe.scale
                            pipe.actualHeight = (pipeImage.naturalHeight || pipeImage.height) * pipe.scale
                        } else {
                            pipe.actualWidth = 100 * pipe.scale
                            pipe.actualHeight = 20 * pipe.scale
                        }
                    } catch (error) {
                        console.warn(`管道 ${pipe.id} 图片加载失败`)
                        this.images.pipes[pipe.id] = null
                        pipe.actualWidth = 100 * pipe.scale
                        pipe.actualHeight = 20 * pipe.scale
                    }
                }

                this.isLoading = false
                this.redraw()
            } catch (error) {
                console.error('图片加载过程中发生错误:', error)
                this.isLoading = false
                // 即使图片加载失败也要绘制基本结构
                this.redraw()
            }
        },

        // 加载单个图片
        loadImage(src) {
            return new Promise((resolve, reject) => {
                const img = new Image()
                img.onload = () => resolve(img)
                img.onerror = () => {
                    console.warn(`图片加载失败: ${src}`)
                    resolve(null) // 返回null而不是reject，避免中断整个加载流程
                }
                img.src = src
            })
        },

        // 重绘Canvas
        redraw() {
            if (!this.ctx) return

            // 清空画布
            this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

            if (this.isLoading) {
                this.drawLoading()
                return
            }

            // 绘制背景
            this.drawBackground()

            // 绘制连接管道
            this.drawPipes()

            // 绘制节点
            this.drawNodes()
        },

        // 绘制加载状态
        drawLoading() {
            this.ctx.fillStyle = '#f0f0f0'
            this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

            this.ctx.fillStyle = '#666'
            this.ctx.font = '16px Arial'
            this.ctx.textAlign = 'center'
            this.ctx.fillText('加载中...', this.canvasWidth / 2, this.canvasHeight / 2)
        },

        // 绘制背景
        drawBackground() {
            if (this.images.background) {
                this.ctx.drawImage(
                    this.images.background,
                    0, 0,
                    this.canvasWidth,
                    this.canvasHeight
                )
            } else {
                // 默认背景色
                this.ctx.fillStyle = '#001529'
                this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
            }
        },

        // 绘制管道图片 - 直接渲染，无复杂计算
        drawPipes() {
            this.pipes.forEach(pipe => {
                const x = pipe.x * this.scale
                const y = pipe.y * this.scale
                const pipeImage = this.images.pipes[pipe.id]

                if (pipeImage) {
                    // 使用预先计算好的尺寸绘制管道图片
                    const scaledWidth = pipe.actualWidth * this.scale
                    const scaledHeight = pipe.actualHeight * this.scale

                    this.ctx.drawImage(pipeImage, x, y, scaledWidth, scaledHeight)
                } else {
                    // 默认绘制矩形占位
                    const scaledWidth = pipe.actualWidth * this.scale
                    const scaledHeight = pipe.actualHeight * this.scale

                    this.ctx.fillStyle = 'rgba(0, 212, 255, 0.3)'
                    this.ctx.fillRect(x, y, scaledWidth, scaledHeight)
                    this.ctx.strokeStyle = '#00d4ff'
                    this.ctx.lineWidth = 2
                    this.ctx.strokeRect(x, y, scaledWidth, scaledHeight)
                }
            })
        },

        // 绘制节点
        drawNodes() {
            this.nodes.forEach(node => {
                const x = node.x * this.scale
                const y = node.y * this.scale
                const nodeImages = this.images.nodes[node.id]

                if (nodeImages && nodeImages.base && nodeImages.top) {
                    // 绘制底座图片（使用预先计算的尺寸和自定义偏移）
                    if (nodeImages.base) {
                        const baseWidth = node.actualWidth * this.scale
                        const baseHeight = node.actualHeight * this.scale

                        // 应用底座图片的自定义偏移
                        const baseOffset = node.baseOffset || { x: 0, y: 0 }
                        const baseX = x + baseOffset.x * this.scale
                        const baseY = y + baseOffset.y * this.scale

                        this.ctx.drawImage(nodeImages.base, baseX, baseY, baseWidth, baseHeight)
                    }

                    // 绘制顶部图片（按原始尺寸，使用自定义偏移）
                    if (nodeImages.top) {
                        const topWidth = (nodeImages.top.naturalWidth || nodeImages.top.width) * node.scale * this.scale
                        const topHeight = (nodeImages.top.naturalHeight || nodeImages.top.height) * node.scale * this.scale

                        // 应用顶部图片的自定义偏移
                        const topOffset = node.topOffset || { x: 0, y: -5 }
                        const centerOffsetX = (node.actualWidth * this.scale - topWidth) / 2
                        const centerOffsetY = (node.actualHeight * this.scale - topHeight) / 2
                        const topX = x + centerOffsetX + topOffset.x * this.scale
                        const topY = y + centerOffsetY + topOffset.y * this.scale

                        this.ctx.drawImage(nodeImages.top, topX, topY, topWidth, topHeight)
                    }

                    // 绘制节点名称
                    this.drawNodeText(node, x, y)
                } else {
                    // 默认绘制节点
                    this.drawDefaultNode(node, x, y)
                }

                // 绘制悬停效果
                if (this.hoveredNode === node.id) {
                    this.drawHoverEffect(x, y)
                }
            })
        },

        // 绘制节点文字
        drawNodeText(node, x, y) {
            this.ctx.fillStyle = '#ffffff'
            this.ctx.font = `bold ${Math.max(12, 14 * this.scale)}px Arial`
            this.ctx.textAlign = 'center'
            this.ctx.textBaseline = 'middle'

            // 添加文字阴影效果
            this.ctx.shadowColor = 'rgba(0, 0, 0, 0.8)'
            this.ctx.shadowBlur = 4
            this.ctx.shadowOffsetX = 1
            this.ctx.shadowOffsetY = 1

            const textY = y + node.actualHeight * this.scale + 20 * this.scale
            this.ctx.fillText(
                node.name,
                x + node.actualWidth * this.scale / 2,
                textY // 文字显示在节点下方
            )

            // 重置阴影
            this.ctx.shadowColor = 'transparent'
            this.ctx.shadowBlur = 0
            this.ctx.shadowOffsetX = 0
            this.ctx.shadowOffsetY = 0
        },

        // 绘制默认节点
        drawDefaultNode(node, x, y) {
            const scaledWidth = node.actualWidth * this.scale
            const scaledHeight = node.actualHeight * this.scale

            // 绘制节点背景
            this.ctx.fillStyle = '#1890ff'
            this.ctx.fillRect(x, y, scaledWidth, scaledHeight)

            // 绘制节点边框
            this.ctx.strokeStyle = '#00fbfb'
            this.ctx.lineWidth = 2
            this.ctx.strokeRect(x, y, scaledWidth, scaledHeight)

            // 绘制节点文字
            this.ctx.fillStyle = '#fff'
            this.ctx.font = `${12 * this.scale}px Arial`
            this.ctx.textAlign = 'center'
            this.ctx.fillText(
                node.name,
                x + scaledWidth / 2,
                y + scaledHeight / 2 + 4 * this.scale
            )
        },

        // 绘制悬停效果
        drawHoverEffect(x, y) {
            const nodeWidth = this.hoveredNode ? this.nodes.find(n => n.id === this.hoveredNode).actualWidth * this.scale : 140 * this.scale
            const nodeHeight = this.hoveredNode ? this.nodes.find(n => n.id === this.hoveredNode).actualHeight * this.scale : 100 * this.scale

            this.ctx.strokeStyle = '#52c41a'
            this.ctx.lineWidth = 3
            this.ctx.setLineDash([])
            this.ctx.strokeRect(x - 2, y - 2, nodeWidth + 4, nodeHeight + 4)
        },

        // Canvas点击事件
        handleCanvasClick(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            // 检查是否点击了节点
            const clickedNode = this.getNodeAtPosition(x, y)

            if (clickedNode) {
                this.showNodeMenu(clickedNode)
            } else {
                this.closeMenu()
            }
        },

        // Canvas鼠标移动事件
        handleCanvasMouseMove(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            // 检查是否悬停在节点上
            const hoveredNode = this.getNodeAtPosition(x, y)

            if (hoveredNode) {
                this.hoveredNode = hoveredNode.id
                this.canvas.style.cursor = 'pointer'
            } else {
                this.hoveredNode = null
                this.canvas.style.cursor = 'default'
            }

            this.redraw()
        },

        // Canvas鼠标离开事件
        handleCanvasMouseLeave() {
            this.hoveredNode = null
            this.canvas.style.cursor = 'default'
            this.redraw()
        },

        // 获取指定位置的节点
        getNodeAtPosition(x, y) {
            for (const node of this.nodes) {
                const nodeX = node.x * this.scale
                const nodeY = node.y * this.scale
                const nodeWidth = (node.actualWidth || 140) * this.scale
                const nodeHeight = (node.actualHeight || 100) * this.scale

                // 扩大点击区域，包含文字区域
                const textAreaHeight = 25 * this.scale
                const totalHeight = nodeHeight + textAreaHeight

                if (x >= nodeX && x <= nodeX + nodeWidth &&
                    y >= nodeY && y <= nodeY + totalHeight) {
                    return node
                }
            }
            return null
        },

        // 显示节点菜单
        showNodeMenu(node) {
            this.currentNode = node

            // 计算菜单位置（节点右上角）
            const rect = this.canvas.getBoundingClientRect()
            const nodeX = node.x * this.scale + rect.left
            const nodeY = node.y * this.scale + rect.top
            const nodeWidth = (node.actualWidth || 140) * this.scale

            // 菜单显示在节点右上角
            let menuX = nodeX + nodeWidth + 10
            let menuY = nodeY

            // 防止菜单超出视口
            const menuWidth = 200
            const menuHeight = node.menuItems.length * 40 + 60

            if (menuX + menuWidth > window.innerWidth) {
                menuX = nodeX - menuWidth - 10
            }

            if (menuY + menuHeight > window.innerHeight) {
                menuY = window.innerHeight - menuHeight - 10
            }

            this.menuStyle = {
                left: menuX + 'px',
                top: menuY + 'px'
            }

            this.showMenu = true
        },

        // 关闭菜单
        closeMenu() {
            this.showMenu = false
            this.currentNode = null
        },

        // 菜单项点击事件
        handleMenuItemClick(item) {
            console.log('菜单项点击:', {
                node: this.currentNode.name,
                action: item.action,
                label: item.label
            })

            // 这里可以根据action执行不同的操作
            switch (item.action) {
                case 'view':
                    this.handleViewAction()
                    break
                case 'edit':
                    this.handleEditAction()
                    break
                case 'analyze':
                    this.handleAnalyzeAction()
                    break
                default:
                    this.$message.info(`执行操作: ${item.label}`)
            }

            this.closeMenu()
        },

        // 查看详情操作
        handleViewAction() {
            this.$message.info(`查看 ${this.currentNode.name} 的详细信息`)
            // 这里可以打开详情页面或模态框
        },

        // 编辑配置操作
        handleEditAction() {
            this.$message.info(`编辑 ${this.currentNode.name} 的配置`)
            // 这里可以打开编辑页面或模态框
        },

        // 数据分析操作
        handleAnalyzeAction() {
            this.$message.info(`分析 ${this.currentNode.name} 的数据`)
            // 这里可以打开分析页面或模态框
        },

        // 绑定事件
        bindEvents() {
            window.addEventListener('resize', this.handleResize)
        },

        // 解绑事件
        unbindEvents() {
            window.removeEventListener('resize', this.handleResize)
        },

        // 窗口大小改变事件
        handleResize() {
            this.resizeCanvas()
        }
    }
}
</script>

<style lang="less" scoped>
.sskj-mom-menu-container {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #001529;
    overflow: hidden;

    .canvas-container {
        width: 100%;
        height: 100%;
        position: relative;

        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
    }

    .node-menu {
        position: fixed;
        width: 200px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        overflow: hidden;

        .menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f5f5f5;
            border-bottom: 1px solid #e8e8e8;

            .menu-title {
                font-weight: 500;
                color: #262626;
                font-size: 14px;
            }

            .close-btn {
                cursor: pointer;
                color: #8c8c8c;
                font-size: 12px;

                &:hover {
                    color: #262626;
                }
            }
        }

        .menu-content {
            padding: 8px 0;

            .menu-item {
                display: flex;
                align-items: center;
                padding: 8px 16px;
                cursor: pointer;
                transition: background-color 0.2s;

                &:hover {
                    background: #f5f5f5;
                }

                .anticon {
                    margin-right: 8px;
                    color: #1890ff;
                    font-size: 14px;
                }

                span {
                    color: #262626;
                    font-size: 14px;
                }
            }
        }
    }

    .menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 999;
        background: transparent;
    }
}

/* 暗色主题适配 */
body.has-dark-theme .sskj-mom-menu-container,
.sskj-mom-menu-container.dark-theme {
    .node-menu {
        background: #1f1f1f;
        border: 1px solid #303030;

        .menu-header {
            background: #262626;
            border-bottom-color: #303030;

            .menu-title {
                color: #fff;
            }

            .close-btn {
                color: #8c8c8c;

                &:hover {
                    color: #fff;
                }
            }
        }

        .menu-content {
            .menu-item {
                &:hover {
                    background: #262626;
                }

                .anticon {
                    color: #1890ff;
                }

                span {
                    color: #fff;
                }
            }
        }
    }
}

/* 响应式适配 */
@media (max-width: 768px) {
    .sskj-mom-menu-container {
        .node-menu {
            width: 180px;

            .menu-header {
                padding: 10px 12px;

                .menu-title {
                    font-size: 13px;
                }
            }

            .menu-content {
                .menu-item {
                    padding: 6px 12px;

                    .anticon {
                        font-size: 13px;
                    }

                    span {
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .sskj-mom-menu-container {
        .node-menu {
            width: 160px;

            .menu-header {
                padding: 8px 10px;

                .menu-title {
                    font-size: 12px;
                }
            }

            .menu-content {
                .menu-item {
                    padding: 5px 10px;

                    .anticon {
                        font-size: 12px;
                        margin-right: 6px;
                    }

                    span {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
</style>