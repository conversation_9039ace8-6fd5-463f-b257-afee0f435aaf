<template>
    <div class="sskj-mom-menu-container">
        <!-- Canvas容器 -->
        <div class="canvas-container" ref="canvasContainer">
            <canvas ref="canvas" @click="handleCanvasClick" @mousemove="handleCanvasMouseMove"
                @mouseleave="handleCanvasMouseLeave"></canvas>
        </div>

        <!-- 节点菜单 -->
        <div v-if="showMenu" class="node-menu" :style="menuStyle" @click.stop>
            <div class="menu-header">
                <span class="menu-title">{{ currentNode.name }}</span>
                <a-icon type="close" class="close-btn" @click="closeMenu" />
            </div>
            <div class="menu-content">
                <div v-for="(item, index) in currentNode.menuItems" :key="index" class="menu-item"
                    @click="handleMenuItemClick(item)">
                    <a-icon :type="item.icon" v-if="item.icon" />
                    <span>{{ item.label }}</span>
                </div>
            </div>
        </div>

        <!-- 遮罩层 -->
        <div v-if="showMenu" class="menu-overlay" @click="closeMenu"></div>
    </div>
</template>

<script>
export default {
    name: 'SSKJMOMMenu',
    data() {
        return {
            // Canvas相关
            canvas: null,
            ctx: null,
            canvasWidth: 0,
            canvasHeight: 0,
            scale: 1, // 缩放比例

            // 图片资源
            images: {
                background: null,
                nodes: {},
                pipes: {}
            },

            // 节点配置
            nodes: [
                {
                    id: 'demand-forecast',
                    name: '需求预测',
                    x: 320, // 原始坐标
                    y: 209,
                      width: 120,
                      height: 80,
                    // 每个节点由两张图片组成：上面的图片和底座
                    // 临时使用现有图片进行测试
                    topImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-base.png'),
                    menuItems: [
                        { label: '查看详情', icon: 'eye', action: 'view' },
                        { label: '编辑配置', icon: 'edit', action: 'edit' },
                        { label: '数据分析', icon: 'bar-chart', action: 'analyze' }
                    ]
                },
                {
                    id: 'recruitment',
                    name: '人员招聘',
                    x: 779,
                    y: 219,
                    width: 120,
                    height: 80,
                    topImagePath: require('@/assets/images/SSKJMomMenu/recruitment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/recruitment-base.png'),
                    menuItems: [
                        { label: '招聘流程', icon: 'user-add', action: 'process' },
                        { label: '面试安排', icon: 'calendar', action: 'interview' },
                        { label: '人员统计', icon: 'team', action: 'statistics' }
                    ]
                },
                {
                    id: 'entry-training',
                    name: '入职培训',
                    x: 284,
                    y: 493,
                    width: 100,
                    height: 60,
                    topImagePath: require('@/assets/images/SSKJMomMenu/entry-training-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/entry-training-base.png'),
                    menuItems: [
                        { label: 'AI配置', icon: 'robot', action: 'ai-config' },
                        { label: '面试记录', icon: 'file-text', action: 'records' },
                        { label: '评估报告', icon: 'file-done', action: 'report' }
                    ]
                },
                {
                    id: 'job-deployment',
                    name: '在职调配',
                    x: 759,
                    y: 589,
                    width: 120,
                    height: 80,
                    topImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/job-deployment-base.png'),
                    menuItems: [
                        { label: '培训计划', icon: 'book', action: 'training' },
                        { label: '技能评估', icon: 'trophy', action: 'assessment' },
                        { label: '证书管理', icon: 'safety-certificate', action: 'certificate' }
                    ]
                },
                {
                    id: 'performance-management',
                    name: '绩效管理',
                    x: 1034,
                    y: 371,
                    width: 120,
                    height: 80,
                    topImagePath: require('@/assets/images/SSKJMomMenu/performance-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/performance-management-base.png'),
                    menuItems: [
                        { label: '绩效考核', icon: 'audit', action: 'evaluation' },
                        { label: '目标设定', icon: 'aim', action: 'target' },
                        { label: '奖惩记录', icon: 'gift', action: 'reward' }
                    ]
                },
                {
                    id: 'promotion-management',
                    name: '晋升管理',
                    x: 1322,
                    y: 211,
                    width: 120,
                    height: 80,
                    topImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/promotion-management-base.png'),
                    menuItems: [
                        { label: '培训课程', icon: 'read', action: 'courses' },
                        { label: '考试安排', icon: 'file-search', action: 'exam' },
                        { label: '培训进度', icon: 'progress', action: 'progress' }
                    ]
                },
                {
                    id: 'resignation-management',
                    name: '离职管理',
                    x: 1486,
                    y: 505,
                    width: 120,
                    height: 80,
                    topImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-top.png'),
                    baseImagePath: require('@/assets/images/SSKJMomMenu/resignation-management-base.png'),
                    menuItems: [
                        { label: '离职流程', icon: 'logout', action: 'process' },
                        { label: '交接安排', icon: 'swap', action: 'handover' },
                        { label: '离职分析', icon: 'pie-chart', action: 'analysis' }
                    ]
                }
            ],

            // 连接管道配置
            pipes: [
                { from: 'demand-forecast', to: 'recruitment', imagePath: require('@/assets/images/SSKJMomMenu/pipe-1.png') },
            ],

            // 菜单相关
            showMenu: false,
            currentNode: null,
            menuStyle: {},

            // 交互状态
            hoveredNode: null,
            isLoading: true
        }
    },

    mounted() {
        this.initCanvas()
        this.loadImages()
        this.bindEvents()
    },

    beforeDestroy() {
        this.unbindEvents()
    },

    methods: {
        // 初始化Canvas
        initCanvas() {
            this.canvas = this.$refs.canvas
            this.ctx = this.canvas.getContext('2d')
            this.resizeCanvas()
        },

        // 调整Canvas大小
        resizeCanvas() {
            const container = this.$refs.canvasContainer
            const containerRect = container.getBoundingClientRect()

            // 设置Canvas尺寸
            this.canvasWidth = containerRect.width
            this.canvasHeight = containerRect.height

            // 设置Canvas实际尺寸
            this.canvas.width = this.canvasWidth * window.devicePixelRatio
            this.canvas.height = this.canvasHeight * window.devicePixelRatio

            // 设置Canvas显示尺寸
            this.canvas.style.width = this.canvasWidth + 'px'
            this.canvas.style.height = this.canvasHeight + 'px'

            // 缩放上下文以适应高DPI
            this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

            // 计算缩放比例（基于1200px宽度的设计稿）
            this.scale = Math.min(this.canvasWidth / 1200, this.canvasHeight / 600)

            this.redraw()
        },

        // 加载图片资源
        async loadImages() {
            try {
                // 加载背景图片
                try {
                    // this.images.background = await this.loadImage(require('@/assets/images/SSKJMomMenu/background.png'))
                } catch (error) {
                    console.warn('背景图片加载失败，将使用默认背景')
                    this.images.background = null
                }

                // 加载节点图片（每个节点有两张图片：顶部图片和底座图片）
                for (const node of this.nodes) {
                    try {
                        // 为每个节点创建一个对象来存储两张图片
                        this.images.nodes[node.id] = {
                            top: await this.loadImage(node.topImagePath),
                            base: await this.loadImage(node.baseImagePath)
                        }
                    } catch (error) {
                        console.warn(`节点 ${node.id} 图片加载失败，将使用默认样式`)
                        this.images.nodes[node.id] = {
                            top: null,
                            base: null
                        }
                    }
                }

                // 加载管道图片
                for (const pipe of this.pipes) {
                    try {
                        this.images.pipes[`${pipe.from}-${pipe.to}`] = await this.loadImage(pipe.imagePath)
                    } catch (error) {
                        console.warn(`管道 ${pipe.from}-${pipe.to} 图片加载失败，将使用默认连接线`)
                        this.images.pipes[`${pipe.from}-${pipe.to}`] = null
                    }
                }

                this.isLoading = false
                this.redraw()
            } catch (error) {
                console.error('图片加载过程中发生错误:', error)
                this.isLoading = false
                // 即使图片加载失败也要绘制基本结构
                this.redraw()
            }
        },

        // 加载单个图片
        loadImage(src) {
            return new Promise((resolve, reject) => {
                const img = new Image()
                img.onload = () => resolve(img)
                img.onerror = () => {
                    console.warn(`图片加载失败: ${src}`)
                    resolve(null) // 返回null而不是reject，避免中断整个加载流程
                }
                img.src = src
            })
        },

        // 重绘Canvas
        redraw() {
            if (!this.ctx) return

            // 清空画布
            this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

            if (this.isLoading) {
                this.drawLoading()
                return
            }

            // 绘制背景
            this.drawBackground()

            // 绘制连接管道
            this.drawPipes()

            // 绘制节点
            this.drawNodes()
        },

        // 绘制加载状态
        drawLoading() {
            this.ctx.fillStyle = '#f0f0f0'
            this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

            this.ctx.fillStyle = '#666'
            this.ctx.font = '16px Arial'
            this.ctx.textAlign = 'center'
            this.ctx.fillText('加载中...', this.canvasWidth / 2, this.canvasHeight / 2)
        },

        // 绘制背景
        drawBackground() {
            if (this.images.background) {
                this.ctx.drawImage(
                    this.images.background,
                    0, 0,
                    this.canvasWidth,
                    this.canvasHeight
                )
            } else {
                // 默认背景色
                this.ctx.fillStyle = '#001529'
                this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
            }
        },

        // 绘制连接管道
        drawPipes() {
            this.pipes.forEach(pipe => {
                const fromNode = this.nodes.find(n => n.id === pipe.from)
                const toNode = this.nodes.find(n => n.id === pipe.to)

                if (!fromNode || !toNode) return

                const pipeImage = this.images.pipes[`${pipe.from}-${pipe.to}`]

                if (pipeImage) {
                    // 计算管道位置和大小
                    const fromX = (fromNode.x + fromNode.width / 2) * this.scale
                    const fromY = (fromNode.y + fromNode.height / 2) * this.scale
                    const toX = (toNode.x + toNode.width / 2) * this.scale
                    const toY = (toNode.y + toNode.height / 2) * this.scale

                    // 绘制管道图片（这里需要根据实际图片调整）
                    this.ctx.drawImage(
                        pipeImage,
                        Math.min(fromX, toX),
                        Math.min(fromY, toY),
                        Math.abs(toX - fromX),
                        Math.abs(toY - fromY)
                    )
                } else {
                    // 默认绘制连接线
                    this.drawDefaultPipe(fromNode, toNode)
                }
            })
        },

        // 绘制默认连接线
        drawDefaultPipe(fromNode, toNode) {
            const fromX = (fromNode.x + fromNode.width / 2) * this.scale
            const fromY = (fromNode.y + fromNode.height / 2) * this.scale
            const toX = (toNode.x + toNode.width / 2) * this.scale
            const toY = (toNode.y + toNode.height / 2) * this.scale

            this.ctx.strokeStyle = '#00fbfb'
            this.ctx.lineWidth = 2
            this.ctx.setLineDash([5, 5])

            this.ctx.beginPath()
            this.ctx.moveTo(fromX, fromY)
            this.ctx.lineTo(toX, toY)
            this.ctx.stroke()

            this.ctx.setLineDash([])
        },

        // 绘制节点
        drawNodes() {
            this.nodes.forEach(node => {
                const x = node.x * this.scale
                const y = node.y * this.scale
                const width = node.width * this.scale
                const height = node.height * this.scale

                const nodeImages = this.images.nodes[node.id]

                if (nodeImages && nodeImages.base && nodeImages.top) {
                    // 先绘制底座图片
                    if (nodeImages.base) {
                        this.ctx.drawImage(nodeImages.base, x, y, width, height)
                    }

                    // 再绘制顶部图片（可能需要调整位置和大小）
                    if (nodeImages.top) {
                        // 顶部图片可能需要稍微偏移或调整大小
                        // 这里假设顶部图片占节点高度的70%，并且居中显示
                        const topHeight = height * 0.7
                        const topWidth = width * 0.8
                        const topX = x + (width - topWidth) / 2
                        const topY = y + (height - topHeight) / 2 - height * 0.1 // 稍微向上偏移

                        this.ctx.drawImage(nodeImages.top, topX, topY, topWidth, topHeight)
                    }
                } else {
                    // 默认绘制节点
                    this.drawDefaultNode(node, x, y, width, height)
                }

                // 绘制悬停效果
                if (this.hoveredNode === node.id) {
                    this.drawHoverEffect(x, y, width, height)
                }
            })
        },

        // 绘制默认节点
        drawDefaultNode(node, x, y, width, height) {
            // 绘制节点背景
            this.ctx.fillStyle = '#1890ff'
            this.ctx.fillRect(x, y, width, height)

            // 绘制节点边框
            this.ctx.strokeStyle = '#00fbfb'
            this.ctx.lineWidth = 2
            this.ctx.strokeRect(x, y, width, height)

            // 绘制节点文字
            this.ctx.fillStyle = '#fff'
            this.ctx.font = `${12 * this.scale}px Arial`
            this.ctx.textAlign = 'center'
            this.ctx.fillText(
                node.name,
                x + width / 2,
                y + height / 2 + 4 * this.scale
            )
        },

        // 绘制悬停效果
        drawHoverEffect(x, y, width, height) {
            this.ctx.strokeStyle = '#52c41a'
            this.ctx.lineWidth = 3
            this.ctx.setLineDash([])
            this.ctx.strokeRect(x - 2, y - 2, width + 4, height + 4)
        },

        // Canvas点击事件
        handleCanvasClick(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            // 检查是否点击了节点
            const clickedNode = this.getNodeAtPosition(x, y)

            if (clickedNode) {
                this.showNodeMenu(clickedNode)
            } else {
                this.closeMenu()
            }
        },

        // Canvas鼠标移动事件
        handleCanvasMouseMove(event) {
            const rect = this.canvas.getBoundingClientRect()
            const x = event.clientX - rect.left
            const y = event.clientY - rect.top

            // 检查是否悬停在节点上
            const hoveredNode = this.getNodeAtPosition(x, y)

            if (hoveredNode) {
                this.hoveredNode = hoveredNode.id
                this.canvas.style.cursor = 'pointer'
            } else {
                this.hoveredNode = null
                this.canvas.style.cursor = 'default'
            }

            this.redraw()
        },

        // Canvas鼠标离开事件
        handleCanvasMouseLeave() {
            this.hoveredNode = null
            this.canvas.style.cursor = 'default'
            this.redraw()
        },

        // 获取指定位置的节点
        getNodeAtPosition(x, y) {
            for (const node of this.nodes) {
                const nodeX = node.x * this.scale
                const nodeY = node.y * this.scale
                const nodeWidth = node.width * this.scale
                const nodeHeight = node.height * this.scale

                if (x >= nodeX && x <= nodeX + nodeWidth &&
                    y >= nodeY && y <= nodeY + nodeHeight) {
                    return node
                }
            }
            return null
        },

        // 显示节点菜单
        showNodeMenu(node) {
            this.currentNode = node

            // 计算菜单位置（节点右上角）
            const rect = this.canvas.getBoundingClientRect()
            const nodeX = node.x * this.scale + rect.left
            const nodeY = node.y * this.scale + rect.top
            const nodeWidth = node.width * this.scale

            // 菜单显示在节点右上角
            let menuX = nodeX + nodeWidth + 10
            let menuY = nodeY

            // 防止菜单超出视口
            const menuWidth = 200
            const menuHeight = node.menuItems.length * 40 + 60

            if (menuX + menuWidth > window.innerWidth) {
                menuX = nodeX - menuWidth - 10
            }

            if (menuY + menuHeight > window.innerHeight) {
                menuY = window.innerHeight - menuHeight - 10
            }

            this.menuStyle = {
                left: menuX + 'px',
                top: menuY + 'px'
            }

            this.showMenu = true
        },

        // 关闭菜单
        closeMenu() {
            this.showMenu = false
            this.currentNode = null
        },

        // 菜单项点击事件
        handleMenuItemClick(item) {
            console.log('菜单项点击:', {
                node: this.currentNode.name,
                action: item.action,
                label: item.label
            })

            // 这里可以根据action执行不同的操作
            switch (item.action) {
                case 'view':
                    this.handleViewAction()
                    break
                case 'edit':
                    this.handleEditAction()
                    break
                case 'analyze':
                    this.handleAnalyzeAction()
                    break
                default:
                    this.$message.info(`执行操作: ${item.label}`)
            }

            this.closeMenu()
        },

        // 查看详情操作
        handleViewAction() {
            this.$message.info(`查看 ${this.currentNode.name} 的详细信息`)
            // 这里可以打开详情页面或模态框
        },

        // 编辑配置操作
        handleEditAction() {
            this.$message.info(`编辑 ${this.currentNode.name} 的配置`)
            // 这里可以打开编辑页面或模态框
        },

        // 数据分析操作
        handleAnalyzeAction() {
            this.$message.info(`分析 ${this.currentNode.name} 的数据`)
            // 这里可以打开分析页面或模态框
        },

        // 绑定事件
        bindEvents() {
            window.addEventListener('resize', this.handleResize)
        },

        // 解绑事件
        unbindEvents() {
            window.removeEventListener('resize', this.handleResize)
        },

        // 窗口大小改变事件
        handleResize() {
            this.resizeCanvas()
        }
    }
}
</script>

<style lang="less" scoped>
.sskj-mom-menu-container {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #001529;
    overflow: hidden;

    .canvas-container {
        width: 100%;
        height: 100%;
        position: relative;

        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
    }

    .node-menu {
        position: fixed;
        width: 200px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        overflow: hidden;

        .menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f5f5f5;
            border-bottom: 1px solid #e8e8e8;

            .menu-title {
                font-weight: 500;
                color: #262626;
                font-size: 14px;
            }

            .close-btn {
                cursor: pointer;
                color: #8c8c8c;
                font-size: 12px;

                &:hover {
                    color: #262626;
                }
            }
        }

        .menu-content {
            padding: 8px 0;

            .menu-item {
                display: flex;
                align-items: center;
                padding: 8px 16px;
                cursor: pointer;
                transition: background-color 0.2s;

                &:hover {
                    background: #f5f5f5;
                }

                .anticon {
                    margin-right: 8px;
                    color: #1890ff;
                    font-size: 14px;
                }

                span {
                    color: #262626;
                    font-size: 14px;
                }
            }
        }
    }

    .menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 999;
        background: transparent;
    }
}

/* 暗色主题适配 */
body.has-dark-theme .sskj-mom-menu-container,
.sskj-mom-menu-container.dark-theme {
    .node-menu {
        background: #1f1f1f;
        border: 1px solid #303030;

        .menu-header {
            background: #262626;
            border-bottom-color: #303030;

            .menu-title {
                color: #fff;
            }

            .close-btn {
                color: #8c8c8c;

                &:hover {
                    color: #fff;
                }
            }
        }

        .menu-content {
            .menu-item {
                &:hover {
                    background: #262626;
                }

                .anticon {
                    color: #1890ff;
                }

                span {
                    color: #fff;
                }
            }
        }
    }
}

/* 响应式适配 */
@media (max-width: 768px) {
    .sskj-mom-menu-container {
        .node-menu {
            width: 180px;

            .menu-header {
                padding: 10px 12px;

                .menu-title {
                    font-size: 13px;
                }
            }

            .menu-content {
                .menu-item {
                    padding: 6px 12px;

                    .anticon {
                        font-size: 13px;
                    }

                    span {
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .sskj-mom-menu-container {
        .node-menu {
            width: 160px;

            .menu-header {
                padding: 8px 10px;

                .menu-title {
                    font-size: 12px;
                }
            }

            .menu-content {
                .menu-item {
                    padding: 5px 10px;

                    .anticon {
                        font-size: 12px;
                        margin-right: 6px;
                    }

                    span {
                        font-size: 12px;
                    }
                }
            }
        }
    }
}
</style>